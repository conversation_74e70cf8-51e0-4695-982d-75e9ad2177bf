# Homyla Frontend-Backend Integration Guide

This guide explains how to run both the React frontend and Django backend together.

## 🚀 Quick Start

### 1. Start the Django Backend

```bash
# Navigate to backend directory
cd /var/www/homyla/homyla_backend

# Activate virtual environment
source myenv/bin/activate

# Install dependencies (if not already installed)
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Start the Django development server
python manage.py runserver 8000
```

The Django backend will be available at: `http://localhost:8000`
API documentation (Swagger): `http://localhost:8000/api/`

### 2. Start the React Frontend

```bash
# Navigate to frontend directory
cd /var/www/homyla/homyla_frontend/my-app

# Install dependencies (if not already installed)
npm install

# Start the React development server
npm start
```

The React frontend will be available at: `http://localhost:3000`

## 🔧 Configuration

### Environment Variables

The React app uses the following environment variables (configured in `.env`):

```env
REACT_APP_API_BASE_URL=http://localhost:8000/api
REACT_APP_API_VERSION=v1
REACT_APP_JWT_ACCESS_TOKEN_LIFETIME=5
REACT_APP_JWT_REFRESH_TOKEN_LIFETIME=1440
```

### CORS Configuration

The Django backend has been configured with CORS headers to allow requests from:
- `http://localhost:3000` (React development server)
- `http://127.0.0.1:3000` (Alternative React development server)

## 🧪 Testing the Integration

### 1. API Connection Test

Visit `http://localhost:3000/api-test` to run automated tests that verify:
- Connection to Django backend
- Accommodations API endpoint
- Bookings API endpoint
- Reviews API endpoint

### 2. Authentication Flow

1. Visit `http://localhost:3000/register` to create a new account
2. Visit `http://localhost:3000/login` to sign in
3. After login, you'll be redirected to the dashboard
4. The dashboard shows your profile information and API connection status

### 3. Browse Accommodations

1. From the dashboard, click "Browse Accommodations"
2. This will fetch and display accommodations from the Django API
3. You can filter accommodations by city, type, and price range

## 📁 Project Structure

### Frontend (`/var/www/homyla/homyla_frontend/my-app/`)
```
src/
├── components/
│   ├── auth/
│   │   ├── Login.js
│   │   ├── Register.js
│   │   └── Auth.css
│   ├── Dashboard.js
│   ├── AccommodationsList.js
│   ├── ApiTest.js
│   └── ProtectedRoute.js
├── contexts/
│   └── AuthContext.js
├── services/
│   └── api.js
├── config/
│   └── config.js
└── App.js
```

### Backend (`/var/www/homyla/homyla_backend/`)
```
api/
├── views.py          # API endpoints
├── serializers.py    # Data serialization
├── urls.py          # API routing
└── permissions.py   # Access control

nomadpersia/
└── settings.py      # Django configuration (includes CORS)
```

## 🔐 Authentication

The integration uses JWT (JSON Web Tokens) for authentication:

1. **Login**: User credentials are sent to `/api/auth/login/`
2. **Token Storage**: Access and refresh tokens are stored in localStorage
3. **Automatic Refresh**: Expired access tokens are automatically refreshed
4. **Protected Routes**: React routes require authentication
5. **API Requests**: All API requests include the JWT token in headers

## 🛠 Available Features

### Implemented Features
- ✅ User registration and login
- ✅ JWT token management with automatic refresh
- ✅ Protected routes in React
- ✅ Dashboard with user profile and stats
- ✅ Accommodations listing with filters
- ✅ API connection testing
- ✅ CORS configuration
- ✅ Error handling and loading states

### API Endpoints Available
- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login
- `POST /api/auth/refresh/` - Token refresh
- `GET /api/auth/profile/` - User profile
- `GET /api/v1/accommodations/` - List accommodations
- `GET /api/v1/bookings/` - List bookings
- `GET /api/v1/reviews/` - List reviews

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure Django backend is running
   - Check that CORS settings include `http://localhost:3000`
   - Verify `django-cors-headers` is installed

2. **API Connection Failed**
   - Check that Django is running on port 8000
   - Verify the API base URL in `.env` file
   - Check browser network tab for detailed error messages

3. **Authentication Issues**
   - Clear localStorage: `localStorage.clear()`
   - Check JWT token expiration
   - Verify Django JWT settings

4. **Module Not Found Errors**
   - Run `npm install` in the React app directory
   - Run `pip install -r requirements.txt` in Django directory

### Debug Steps

1. Check Django logs in the terminal
2. Open browser Developer Tools → Network tab
3. Visit `/api-test` route to run connection tests
4. Check console for JavaScript errors

## 🚀 Next Steps

To extend this integration, you can:

1. Add more React components for bookings management
2. Implement real-time notifications
3. Add file upload for accommodation images
4. Create admin dashboard
5. Add payment integration
6. Implement search and filtering
7. Add map integration for accommodations

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Django and React logs
3. Test API endpoints directly using the Swagger documentation
4. Verify all dependencies are installed correctly
