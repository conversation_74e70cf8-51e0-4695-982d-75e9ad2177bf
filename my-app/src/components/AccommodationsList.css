/* Accommodations List Component Styles */

.accommodations-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.accommodations-header {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 20px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h1 {
  color: #333;
  font-size: 28px;
  font-weight: 600;
  margin: 0;
}

.back-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
}

.back-button:hover {
  background: #5a6268;
}

.accommodations-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
}

/* Filters Section */
.filters-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e5e9;
}

.filters-section h3 {
  color: #333;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  border-bottom: 2px solid #667eea;
  padding-bottom: 8px;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  color: #333;
  font-weight: 500;
  margin-bottom: 6px;
  font-size: 14px;
}

.filter-group input,
.filter-group select {
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: #667eea;
}

.filter-actions {
  display: flex;
  align-items: end;
}

.clear-filters-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-filters-btn:hover {
  background: #5a6268;
}

/* Results Section */
.results-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e5e9;
}

.results-header {
  margin-bottom: 24px;
}

.results-header h3 {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.error-message {
  background: #fee;
  color: #c53030;
  padding: 16px 20px;
  border-radius: 8px;
  border: 1px solid #fed7d7;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.retry-btn {
  background: #c53030;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background: #a02622;
}

.loading-container {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.no-results {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-results p {
  font-size: 18px;
  margin: 0 0 20px 0;
}

/* Accommodations Grid */
.accommodations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.accommodation-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e5e9;
  transition: all 0.2s ease;
  cursor: pointer;
}

.accommodation-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.accommodation-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.accommodation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #e9ecef;
  color: #6c757d;
  font-size: 48px;
}

.accommodation-info {
  padding: 20px;
}

.accommodation-info h4 {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.accommodation-info p {
  color: #666;
  font-size: 14px;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.accommodation-info p:last-of-type {
  margin-bottom: 16px;
}

.price-rating {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
}

.price {
  color: #667eea;
  font-weight: 600;
  font-size: 16px;
}

.rating {
  color: #ffc107;
  font-weight: 500;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .header-content h1 {
    font-size: 24px;
  }
  
  .accommodations-content {
    padding: 20px 15px;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .accommodations-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .filters-section,
  .results-section {
    padding: 20px 16px;
  }
}

@media (max-width: 480px) {
  .accommodation-card {
    margin: 0 -5px;
  }
  
  .accommodation-info {
    padding: 16px;
  }
  
  .accommodation-info h4 {
    font-size: 16px;
  }
  
  .price-rating {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
