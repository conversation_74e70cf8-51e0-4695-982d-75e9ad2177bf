/**
 * Accommodations List Component
 * Displays a list of accommodations from the Django API
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import apiService from '../services/api';
import './AccommodationsList.css';

const AccommodationsList = () => {
  const [accommodations, setAccommodations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    city: '',
    accommodation_type: '',
    min_price: '',
    max_price: '',
  });
  
  const { } = useAuth();
  const navigate = useNavigate();

  const fetchAccommodations = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Build query parameters
      const params = {};
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          params[key] = filters[key];
        }
      });

      const response = await apiService.accommodations.getAll(params);
      
      // Handle both paginated and non-paginated responses
      const accommodationsData = response.results || response;
      setAccommodations(Array.isArray(accommodationsData) ? accommodationsData : []);
    } catch (err) {
      setError('Failed to load accommodations. Please try again.');
      console.error('Accommodations fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchAccommodations();
  }, [fetchAccommodations]);

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      city: '',
      accommodation_type: '',
      min_price: '',
      max_price: '',
    });
  };

  const handleAccommodationClick = (id) => {
    navigate(`/accommodations/${id}`);
  };

  return (
    <div className="accommodations-container">
      <header className="accommodations-header">
        <div className="header-content">
          <h1>Accommodations</h1>
          <button 
            onClick={() => navigate('/dashboard')}
            className="back-button"
          >
            ← Back to Dashboard
          </button>
        </div>
      </header>

      <div className="accommodations-content">
        {/* Filters Section */}
        <div className="filters-section">
          <h3>Filter Accommodations</h3>
          <div className="filters-grid">
            <div className="filter-group">
              <label htmlFor="search">Search</label>
              <input
                type="text"
                id="search"
                name="search"
                value={filters.search}
                onChange={handleFilterChange}
                placeholder="Search accommodations..."
              />
            </div>
            
            <div className="filter-group">
              <label htmlFor="city">City</label>
              <select
                id="city"
                name="city"
                value={filters.city}
                onChange={handleFilterChange}
              >
                <option value="">All Cities</option>
                <option value="tehran">Tehran</option>
                <option value="shiraz">Shiraz</option>
                <option value="mashhad">Mashhad</option>
              </select>
            </div>
            
            <div className="filter-group">
              <label htmlFor="accommodation_type">Type</label>
              <select
                id="accommodation_type"
                name="accommodation_type"
                value={filters.accommodation_type}
                onChange={handleFilterChange}
              >
                <option value="">All Types</option>
                <option value="hotel">Hotel</option>
                <option value="villa">Villa</option>
                <option value="suite">Suite</option>
              </select>
            </div>
            
            <div className="filter-group">
              <label htmlFor="min_price">Min Price</label>
              <input
                type="number"
                id="min_price"
                name="min_price"
                value={filters.min_price}
                onChange={handleFilterChange}
                placeholder="Min price"
                min="0"
              />
            </div>
            
            <div className="filter-group">
              <label htmlFor="max_price">Max Price</label>
              <input
                type="number"
                id="max_price"
                name="max_price"
                value={filters.max_price}
                onChange={handleFilterChange}
                placeholder="Max price"
                min="0"
              />
            </div>
            
            <div className="filter-actions">
              <button onClick={clearFilters} className="clear-filters-btn">
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div className="results-section">
          {error && (
            <div className="error-message">
              {error}
              <button onClick={fetchAccommodations} className="retry-btn">
                Retry
              </button>
            </div>
          )}

          {loading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Loading accommodations...</p>
            </div>
          ) : (
            <>
              <div className="results-header">
                <h3>
                  {accommodations.length} accommodation{accommodations.length !== 1 ? 's' : ''} found
                </h3>
              </div>

              {accommodations.length === 0 ? (
                <div className="no-results">
                  <p>No accommodations found matching your criteria.</p>
                  <button onClick={clearFilters} className="clear-filters-btn">
                    Clear Filters
                  </button>
                </div>
              ) : (
                <div className="accommodations-grid">
                  {accommodations.map((accommodation) => (
                    <div
                      key={accommodation.id}
                      className="accommodation-card"
                      onClick={() => handleAccommodationClick(accommodation.id)}
                    >
                      <div className="accommodation-image">
                        {accommodation.image_placeholder ? (
                          <img 
                            src={accommodation.image_placeholder} 
                            alt={accommodation.name}
                            onError={(e) => {
                              e.target.src = 'https://via.placeholder.com/300x200?text=No+Image';
                            }}
                          />
                        ) : (
                          <div className="placeholder-image">
                            <span>📷</span>
                          </div>
                        )}
                      </div>
                      
                      <div className="accommodation-info">
                        <h4>{accommodation.name}</h4>
                        <p className="location">
                          📍 {accommodation.city_display || accommodation.city}
                          {accommodation.town && `, ${accommodation.town}`}
                        </p>
                        <p className="type">
                          🏠 {accommodation.accommodation_type_display || accommodation.accommodation_type}
                        </p>
                        <p className="capacity">
                          👥 Up to {accommodation.max_capacity} guests
                        </p>
                        <div className="price-rating">
                          <span className="price">
                            ${accommodation.price_per_night}/night
                          </span>
                          {accommodation.average_rating && (
                            <span className="rating">
                              ⭐ {parseFloat(accommodation.average_rating).toFixed(1)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AccommodationsList;
