/**
 * API Test Component
 * Simple component to test the connection to Django backend
 */

import React, { useState } from 'react';
import apiService from '../services/api';
import config from '../config/config';

const ApiTest = () => {
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState(false);

  const runApiTest = async (testName, testFunction) => {
    setLoading(true);
    try {
      const result = await testFunction();
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: true, data: result, error: null }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: false, data: null, error: error.message }
      }));
    }
    setLoading(false);
  };

  const tests = [
    {
      name: 'Accommodations API',
      description: 'Test fetching accommodations from Django backend',
      test: () => apiService.accommodations.getAll()
    },
    {
      name: 'Bookings API',
      description: 'Test fetching bookings from Django backend',
      test: () => apiService.bookings.getAll()
    },
    {
      name: 'Reviews API',
      description: 'Test fetching reviews from Django backend',
      test: () => apiService.reviews.getAll()
    }
  ];

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>API Connection Test</h2>
      <p>Test the connection between React frontend and Django backend</p>
      
      <div style={{ 
        background: '#f8f9fa', 
        padding: '16px', 
        borderRadius: '8px', 
        marginBottom: '20px' 
      }}>
        <h3>Configuration</h3>
        <p><strong>API Base URL:</strong> {config.api.baseURL}</p>
        <p><strong>API Version:</strong> {config.api.version}</p>
        <p><strong>Environment:</strong> {config.app.environment}</p>
      </div>

      <div style={{ display: 'grid', gap: '16px' }}>
        {tests.map((test, index) => (
          <div key={index} style={{
            border: '1px solid #e1e5e9',
            borderRadius: '8px',
            padding: '16px',
            background: 'white'
          }}>
            <h4>{test.name}</h4>
            <p style={{ color: '#666', fontSize: '14px' }}>{test.description}</p>
            
            <button
              onClick={() => runApiTest(test.name, test.test)}
              disabled={loading}
              style={{
                background: '#667eea',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '4px',
                cursor: loading ? 'not-allowed' : 'pointer',
                marginBottom: '12px'
              }}
            >
              {loading ? 'Testing...' : 'Run Test'}
            </button>

            {testResults[test.name] && (
              <div style={{
                padding: '12px',
                borderRadius: '4px',
                background: testResults[test.name].success ? '#d4edda' : '#f8d7da',
                border: `1px solid ${testResults[test.name].success ? '#c3e6cb' : '#f5c6cb'}`,
                color: testResults[test.name].success ? '#155724' : '#721c24'
              }}>
                <strong>
                  {testResults[test.name].success ? '✅ Success' : '❌ Failed'}
                </strong>
                {testResults[test.name].success ? (
                  <div style={{ marginTop: '8px' }}>
                    <p>Data received successfully!</p>
                    <details>
                      <summary style={{ cursor: 'pointer' }}>View Response</summary>
                      <pre style={{
                        background: '#f8f9fa',
                        padding: '8px',
                        borderRadius: '4px',
                        fontSize: '12px',
                        overflow: 'auto',
                        maxHeight: '200px',
                        marginTop: '8px'
                      }}>
                        {JSON.stringify(testResults[test.name].data, null, 2)}
                      </pre>
                    </details>
                  </div>
                ) : (
                  <div style={{ marginTop: '8px' }}>
                    <p>Error: {testResults[test.name].error}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      <div style={{
        marginTop: '30px',
        padding: '16px',
        background: '#e7f3ff',
        border: '1px solid #b3d9ff',
        borderRadius: '8px'
      }}>
        <h3>Instructions</h3>
        <ol>
          <li>Make sure your Django backend is running on <code>{config.api.baseURL.replace('/api', '')}</code></li>
          <li>Ensure CORS is properly configured in Django settings</li>
          <li>Click "Run Test" for each API endpoint to verify connectivity</li>
          <li>Check the browser's Network tab for detailed request/response information</li>
        </ol>
      </div>
    </div>
  );
};

export default ApiTest;
