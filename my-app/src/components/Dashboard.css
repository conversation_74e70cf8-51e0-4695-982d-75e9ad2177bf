/* Dashboard Component Styles */

.dashboard-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.dashboard-header {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 20px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h1 {
  color: #333;
  font-size: 28px;
  font-weight: 600;
  margin: 0;
}

.logout-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.logout-button:hover {
  background: #c82333;
}

.dashboard-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
}

.error-banner {
  background: #fee;
  color: #c53030;
  padding: 12px 20px;
  border-radius: 8px;
  border: 1px solid #fed7d7;
  margin-bottom: 20px;
  text-align: center;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.dashboard-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e5e9;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.dashboard-card h2 {
  color: #333;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  border-bottom: 2px solid #667eea;
  padding-bottom: 8px;
}

/* Profile Card */
.profile-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.info-item span {
  color: #333;
  font-weight: 400;
}

/* Stats Card */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* Actions Card */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.action-button {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  text-align: center;
  font-size: 14px;
}

.action-button.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.action-button.secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #e1e5e9;
}

.action-button.secondary:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

/* Test Card */
.test-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.test-card h2 {
  color: white;
  border-bottom-color: rgba(255, 255, 255, 0.3);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 16px 0;
  font-weight: 500;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.status-indicator.success {
  background: #28a745;
  box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
}

.api-info {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.api-info small {
  opacity: 0.8;
  font-size: 12px;
}

.loading {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
  }
  
  .header-content h1 {
    font-size: 24px;
  }
  
  .dashboard-main {
    padding: 20px 15px;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .dashboard-card {
    padding: 20px 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .stat-number {
    font-size: 28px;
  }
}
