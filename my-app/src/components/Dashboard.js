/**
 * Dashboard Component
 * Main dashboard showing user information and quick actions
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import apiService from '../services/api';
import './Dashboard.css';

const Dashboard = () => {
  const { user, logout, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    accommodations: 0,
    bookings: 0,
    reviews: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // Fetch user's accommodations, bookings, and reviews
        const [accommodationsRes, bookingsRes, reviewsRes] = await Promise.allSettled([
          apiService.accommodations.getAll(),
          apiService.bookings.getAll(),
          apiService.reviews.getAll(),
        ]);

        setStats({
          accommodations: accommodationsRes.status === 'fulfilled' ? accommodationsRes.value.count || accommodationsRes.value.length || 0 : 0,
          bookings: bookingsRes.status === 'fulfilled' ? bookingsRes.value.count || bookingsRes.value.length || 0 : 0,
          reviews: reviewsRes.status === 'fulfilled' ? reviewsRes.value.count || reviewsRes.value.length || 0 : 0,
        });
      } catch (err) {
        setError('Failed to load dashboard data');
        console.error('Dashboard data fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [isAuthenticated, navigate]);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="dashboard-container">
      <header className="dashboard-header">
        <div className="header-content">
          <h1>Welcome back, {user?.first_name || user?.username}!</h1>
          <button onClick={handleLogout} className="logout-button">
            Logout
          </button>
        </div>
      </header>

      <main className="dashboard-main">
        {error && (
          <div className="error-banner">
            {error}
          </div>
        )}

        <div className="dashboard-grid">
          {/* User Profile Card */}
          <div className="dashboard-card profile-card">
            <h2>Profile Information</h2>
            <div className="profile-info">
              <div className="info-item">
                <label>Name:</label>
                <span>{user?.first_name} {user?.last_name}</span>
              </div>
              <div className="info-item">
                <label>Email:</label>
                <span>{user?.email}</span>
              </div>
              <div className="info-item">
                <label>Username:</label>
                <span>{user?.username}</span>
              </div>
              <div className="info-item">
                <label>Role:</label>
                <span>{user?.profile?.role || 'Guest'}</span>
              </div>
            </div>
            <button 
              className="action-button secondary"
              onClick={() => navigate('/profile')}
            >
              Edit Profile
            </button>
          </div>

          {/* Stats Cards */}
          <div className="dashboard-card stats-card">
            <h2>Quick Stats</h2>
            {loading ? (
              <div className="loading">Loading stats...</div>
            ) : (
              <div className="stats-grid">
                <div className="stat-item">
                  <div className="stat-number">{stats.accommodations}</div>
                  <div className="stat-label">Accommodations</div>
                </div>
                <div className="stat-item">
                  <div className="stat-number">{stats.bookings}</div>
                  <div className="stat-label">Bookings</div>
                </div>
                <div className="stat-item">
                  <div className="stat-number">{stats.reviews}</div>
                  <div className="stat-label">Reviews</div>
                </div>
              </div>
            )}
          </div>

          {/* Quick Actions */}
          <div className="dashboard-card actions-card">
            <h2>Quick Actions</h2>
            <div className="actions-grid">
              <button 
                className="action-button primary"
                onClick={() => navigate('/accommodations')}
              >
                Browse Accommodations
              </button>
              <button 
                className="action-button primary"
                onClick={() => navigate('/bookings')}
              >
                My Bookings
              </button>
              {user?.profile?.role === 'host' && (
                <button 
                  className="action-button primary"
                  onClick={() => navigate('/accommodations/create')}
                >
                  Add Property
                </button>
              )}
              <button 
                className="action-button secondary"
                onClick={() => navigate('/reviews')}
              >
                My Reviews
              </button>
            </div>
          </div>

          {/* API Connection Test */}
          <div className="dashboard-card test-card">
            <h2>API Connection Test</h2>
            <p>This dashboard successfully connected to your Django backend!</p>
            <div className="connection-status">
              <span className="status-indicator success"></span>
              <span>Backend Connected</span>
            </div>
            <div className="api-info">
              <small>
                API Base URL: {process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api'}
              </small>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
