/**
 * Application Configuration
 * Centralized configuration management for the React app
 */

const config = {
  // API Configuration
  api: {
    baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api',
    version: process.env.REACT_APP_API_VERSION || 'v1',
    timeout: 10000, // 10 seconds
  },

  // Authentication Configuration
  auth: {
    tokenKey: 'homyla_access_token',
    refreshTokenKey: 'homyla_refresh_token',
    accessTokenLifetime: parseInt(process.env.REACT_APP_JWT_ACCESS_TOKEN_LIFETIME) || 5, // minutes
    refreshTokenLifetime: parseInt(process.env.REACT_APP_JWT_REFRESH_TOKEN_LIFETIME) || 1440, // minutes (24 hours)
  },

  // App Configuration
  app: {
    name: process.env.REACT_APP_NAME || 'Homyla',
    version: process.env.REACT_APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  },

  // API Endpoints
  endpoints: {
    // Authentication
    login: '/auth/login/',
    register: '/auth/register/',
    refresh: '/auth/refresh/',
    verify: '/auth/verify/',
    profile: '/auth/profile/',

    // Accommodations
    accommodations: '/v1/accommodations/',
    accommodationDetail: (id) => `/v1/accommodations/${id}/`,

    // Bookings
    bookings: '/v1/bookings/',
    bookingDetail: (id) => `/v1/bookings/${id}/`,

    // Reviews
    reviews: '/v1/reviews/',
    reviewDetail: (id) => `/v1/reviews/${id}/`,
  },

  // Development Configuration
  development: {
    enableLogging: process.env.NODE_ENV === 'development',
    enableDebugMode: process.env.NODE_ENV === 'development',
  },
};

export default config;
