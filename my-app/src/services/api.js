/**
 * API Service
 * Centralized API communication service with authentication support
 */

import axios from 'axios';
import config from '../config/config';

// Create axios instance with default configuration
const api = axios.create({
  baseURL: config.api.baseURL,
  timeout: config.api.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management utilities
const tokenManager = {
  getAccessToken: () => localStorage.getItem(config.auth.tokenKey),
  getRefreshToken: () => localStorage.getItem(config.auth.refreshTokenKey),
  setTokens: (accessToken, refreshToken) => {
    localStorage.setItem(config.auth.tokenKey, accessToken);
    if (refreshToken) {
      localStorage.setItem(config.auth.refreshTokenKey, refreshToken);
    }
  },
  clearTokens: () => {
    localStorage.removeItem(config.auth.tokenKey);
    localStorage.removeItem(config.auth.refreshTokenKey);
  },
  isTokenExpired: (token) => {
    if (!token) return true;
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  },
};

// Request interceptor to add authentication token
api.interceptors.request.use(
  (config) => {
    const token = tokenManager.getAccessToken();
    if (token && !tokenManager.isTokenExpired(token)) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refreshToken = tokenManager.getRefreshToken();
      if (refreshToken && !tokenManager.isTokenExpired(refreshToken)) {
        try {
          const response = await axios.post(
            `${config.api.baseURL}${config.endpoints.refresh}`,
            { refresh: refreshToken }
          );
          
          const { access } = response.data;
          tokenManager.setTokens(access);
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return api(originalRequest);
        } catch (refreshError) {
          // Refresh failed, clear tokens and redirect to login
          tokenManager.clearTokens();
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      } else {
        // No valid refresh token, clear tokens and redirect to login
        tokenManager.clearTokens();
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// API service methods
const apiService = {
  // Authentication methods
  auth: {
    login: async (credentials) => {
      const response = await api.post(config.endpoints.login, credentials);
      const { access, refresh } = response.data;
      tokenManager.setTokens(access, refresh);
      return response.data;
    },

    register: async (userData) => {
      const response = await api.post(config.endpoints.register, userData);
      return response.data;
    },

    logout: () => {
      tokenManager.clearTokens();
    },

    getCurrentUser: async () => {
      const response = await api.get(config.endpoints.profile);
      return response.data;
    },

    refreshToken: async () => {
      const refreshToken = tokenManager.getRefreshToken();
      if (!refreshToken) throw new Error('No refresh token available');
      
      const response = await api.post(config.endpoints.refresh, {
        refresh: refreshToken,
      });
      
      const { access } = response.data;
      tokenManager.setTokens(access);
      return response.data;
    },
  },

  // Accommodations methods
  accommodations: {
    getAll: async (params = {}) => {
      const response = await api.get(config.endpoints.accommodations, { params });
      return response.data;
    },

    getById: async (id) => {
      const response = await api.get(config.endpoints.accommodationDetail(id));
      return response.data;
    },

    create: async (accommodationData) => {
      const response = await api.post(config.endpoints.accommodations, accommodationData);
      return response.data;
    },

    update: async (id, accommodationData) => {
      const response = await api.put(config.endpoints.accommodationDetail(id), accommodationData);
      return response.data;
    },

    delete: async (id) => {
      const response = await api.delete(config.endpoints.accommodationDetail(id));
      return response.data;
    },
  },

  // Bookings methods
  bookings: {
    getAll: async (params = {}) => {
      const response = await api.get(config.endpoints.bookings, { params });
      return response.data;
    },

    getById: async (id) => {
      const response = await api.get(config.endpoints.bookingDetail(id));
      return response.data;
    },

    create: async (bookingData) => {
      const response = await api.post(config.endpoints.bookings, bookingData);
      return response.data;
    },

    update: async (id, bookingData) => {
      const response = await api.put(config.endpoints.bookingDetail(id), bookingData);
      return response.data;
    },

    cancel: async (id) => {
      const response = await api.patch(config.endpoints.bookingDetail(id), { status: 'cancelled' });
      return response.data;
    },
  },

  // Reviews methods
  reviews: {
    getAll: async (params = {}) => {
      const response = await api.get(config.endpoints.reviews, { params });
      return response.data;
    },

    create: async (reviewData) => {
      const response = await api.post(config.endpoints.reviews, reviewData);
      return response.data;
    },
  },
};

export { api, tokenManager, apiService };
export default apiService;
