{% extends 'base.html' %}

{% block title %}Add New Property - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h3 class="mb-0">➕ Add New Property</h3>
                    <small class="text-muted">List your accommodation on NomadPersia</small>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <strong>Please correct the errors below:</strong>
                            <ul class="mb-0 mt-2">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">🏨 Basic Information</h5>
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">Property Name *</label>
                                    <input type="text" class="form-control" name="name" 
                                           id="{{ form.name.id_for_label }}" 
                                           value="{{ form.name.value|default:'' }}" required
                                           placeholder="e.g., Beautiful Tehran Hotel">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.accommodation_type.id_for_label }}" class="form-label">Property Type *</label>
                                    <select class="form-select" name="accommodation_type" 
                                            id="{{ form.accommodation_type.id_for_label }}" required>
                                        <option value="">Select type</option>
                                        <option value="villa_suite" {% if form.accommodation_type.value == 'villa_suite' %}selected{% endif %}>Villa & Suite</option>
                                        <option value="apartment" {% if form.accommodation_type.value == 'apartment' %}selected{% endif %}>Apartment</option>
                                        <option value="cottage" {% if form.accommodation_type.value == 'cottage' %}selected{% endif %}>Cottage</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.city.id_for_label }}" class="form-label">City *</label>
                                    <select class="form-select" name="city" 
                                            id="{{ form.city.id_for_label }}" required>
                                        <option value="">Select city</option>
                                        <option value="tehran" {% if form.city.value == 'tehran' %}selected{% endif %}>Tehran</option>
                                        <option value="shiraz" {% if form.city.value == 'shiraz' %}selected{% endif %}>Shiraz</option>
                                        <option value="mashhad" {% if form.city.value == 'mashhad' %}selected{% endif %}>Mashhad</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.star_rating.id_for_label }}" class="form-label">Star Rating</label>
                                    <select class="form-select" name="star_rating" 
                                            id="{{ form.star_rating.id_for_label }}">
                                        <option value="">No rating</option>
                                        <option value="1" {% if form.star_rating.value == '1' %}selected{% endif %}>1 Star</option>
                                        <option value="2" {% if form.star_rating.value == '2' %}selected{% endif %}>2 Stars</option>
                                        <option value="3" {% if form.star_rating.value == '3' %}selected{% endif %}>3 Stars</option>
                                        <option value="4" {% if form.star_rating.value == '4' %}selected{% endif %}>4 Stars</option>
                                        <option value="5" {% if form.star_rating.value == '5' %}selected{% endif %}>5 Stars</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">📝 Description</h5>
                            <div class="mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">Property Description *</label>
                                <textarea class="form-control" name="description" 
                                          id="{{ form.description.id_for_label }}" 
                                          rows="5" required
                                          placeholder="Describe your property, its features, and what makes it special...">{{ form.description.value|default:'' }}</textarea>
                                <small class="form-text text-muted">Provide a detailed description to attract guests</small>
                            </div>
                        </div>

                        <!-- Pricing -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">💰 Pricing</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.price_per_night.id_for_label }}" class="form-label">Price per Night (IRR) *</label>
                                    <input type="number" class="form-control" name="price_per_night" 
                                           id="{{ form.price_per_night.id_for_label }}" 
                                           value="{{ form.price_per_night.value|default:'' }}" required
                                           min="100000" step="50000"
                                           placeholder="e.g., 1500000">
                                    <small class="form-text text-muted">Set your nightly rate in Iranian Rials</small>
                                </div>
                            </div>
                        </div>

                        <!-- Amenities -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">🌟 Amenities</h5>
                            <div class="mb-3">
                                <label for="{{ form.amenities.id_for_label }}" class="form-label">Available Amenities</label>
                                <textarea class="form-control" name="amenities" 
                                          id="{{ form.amenities.id_for_label }}" 
                                          rows="3"
                                          placeholder="WiFi, Pool, Spa, Restaurant, Parking, Air Conditioning, etc.">{{ form.amenities.value|default:'' }}</textarea>
                                <small class="form-text text-muted">List amenities separated by commas</small>
                            </div>
                        </div>

                        <!-- Images -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">📸 Images</h5>
                            <div class="mb-3">
                                <label for="{{ form.image_placeholder.id_for_label }}" class="form-label">Image Description</label>
                                <textarea class="form-control" name="image_placeholder" 
                                          id="{{ form.image_placeholder.id_for_label }}" 
                                          rows="2"
                                          placeholder="Describe the photos you would upload (e.g., exterior view, lobby, rooms, amenities)">{{ form.image_placeholder.value|default:'' }}</textarea>
                                <small class="form-text text-muted">Describe the images that showcase your property</small>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'accommodations:host_accommodations' %}" class="btn btn-secondary">
                                ← Cancel
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                🚀 List Property
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Listing Tips -->
            <div class="card mt-4 bg-light">
                <div class="card-body">
                    <h6>💡 Tips for a Great Listing</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="small">📸 Quality Photos</h6>
                            <p class="small text-muted">High-quality photos are the most important factor for bookings</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="small">📝 Detailed Description</h6>
                            <p class="small text-muted">Include nearby attractions, transportation, and unique features</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="small">💰 Competitive Pricing</h6>
                            <p class="small text-muted">Research similar properties in your area for competitive rates</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}
</style>
{% endblock %}
