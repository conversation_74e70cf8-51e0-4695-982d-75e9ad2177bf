{% extends 'base.html' %}

{% block title %}My Properties - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>🏨 My Properties</h2>
                    <p class="text-muted">Manage your accommodation listings</p>
                </div>
                <div>
                    <a href="{% url 'accommodations:create' %}" class="btn btn-primary">
                        ➕ Add New Property
                    </a>
                </div>
            </div>

            <!-- Properties List -->
            {% if accommodations %}
                <div class="row">
                    {% for accommodation in accommodations %}
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">{{ accommodation.name }}</h5>
                                <span class="badge bg-{% if accommodation.status == 'active' %}success{% elif accommodation.status == 'pending' %}warning{% else %}secondary{% endif %}">
                                    {% if accommodation.status == 'active' %}✅ Active
                                    {% elif accommodation.status == 'pending' %}⏳ Pending
                                    {% else %}❌ Inactive{% endif %}
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">Location</small>
                                        <div class="fw-bold">{{ accommodation.get_city_display }}{% if accommodation.town %}, {{ accommodation.town }}{% endif %}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Type</small>
                                        <div>{{ accommodation.get_accommodation_type_display }}</div>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">Price per Night</small>
                                        <div class="h6 text-primary">{{ accommodation.price_per_night|floatformat:0 }} IRR</div>
                                        {% if accommodation.extra_guest_fee > 0 %}
                                            <small class="text-muted">+{{ accommodation.extra_guest_fee|floatformat:0 }} IRR/extra guest</small>
                                        {% endif %}
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Capacity</small>
                                        <div>
                                            👥 {{ accommodation.standard_capacity }}{% if accommodation.max_capacity and accommodation.max_capacity != accommodation.standard_capacity %}-{{ accommodation.max_capacity }}{% endif %} guests
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">Layout</small>
                                        <div class="small">
                                            🛏️ {{ accommodation.bedrooms }} bed{{ accommodation.bedrooms|pluralize }} •
                                            🚿 {{ accommodation.bathrooms }} bath{{ accommodation.bathrooms|pluralize }}
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Booking</small>
                                        <div>
                                            {% if accommodation.instant_booking %}
                                                <span class="badge bg-success">⚡ Instant</span>
                                            {% else %}
                                                <span class="badge bg-warning text-dark">📋 Approval</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">Description</small>
                                    <div class="small">{{ accommodation.description|truncatewords:15 }}</div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">Statistics</small>
                                    <div class="small">
                                        📅 {{ accommodation.bookings.count }} booking{{ accommodation.bookings.count|pluralize }}<br>
                                        📅 Created {{ accommodation.created_at|date:"M d, Y" }}
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="d-flex gap-2 flex-wrap">
                                    <a href="{% url 'accommodations:detail' accommodation.id %}" class="btn btn-outline-primary btn-sm">
                                        👁️ View
                                    </a>
                                    <a href="{% url 'accommodations:edit' accommodation.id %}" class="btn btn-outline-secondary btn-sm">
                                        ✏️ Edit
                                    </a>
                                    {% if accommodation.status == 'active' %}
                                    <form method="post" action="{% url 'accommodations:toggle_status' accommodation.id %}" class="d-inline">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-outline-warning btn-sm"
                                                onclick="return confirm('Deactivate this property?')">
                                            ⏸️ Deactivate
                                        </button>
                                    </form>
                                    {% elif accommodation.status == 'inactive' %}
                                    <form method="post" action="{% url 'accommodations:toggle_status' accommodation.id %}" class="d-inline">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-outline-success btn-sm">
                                            ▶️ Activate
                                        </button>
                                    </form>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Properties pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <!-- No Properties -->
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="display-1">🏨</i>
                    </div>
                    <h3>No Properties Listed</h3>
                    <p class="text-muted mb-4">
                        You haven't listed any properties yet. Start earning by adding your first accommodation!
                    </p>
                    <div class="d-flex gap-2 justify-content-center">
                        <a href="{% url 'accommodations:create' %}" class="btn btn-primary btn-lg">
                            ➕ Add Your First Property
                        </a>
                        <a href="{% url 'dashboard:host_dashboard' %}" class="btn btn-outline-secondary">
                            📊 Back to Dashboard
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Host Tips -->
    {% if accommodations %}
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h5>💡 Maximize Your Earnings</h5>
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <div class="mb-2">📸</div>
                            <h6 class="small">Update Photos</h6>
                            <p class="small text-muted">Fresh photos attract more guests</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="mb-2">💰</div>
                            <h6 class="small">Optimize Pricing</h6>
                            <p class="small text-muted">Adjust rates based on demand</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="mb-2">⚡</div>
                            <h6 class="small">Quick Responses</h6>
                            <p class="small text-muted">Fast replies improve rankings</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="mb-2">🌟</div>
                            <h6 class="small">Guest Reviews</h6>
                            <p class="small text-muted">Great service leads to 5-star reviews</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.badge {
    font-size: 0.8em;
}

.btn-sm {
    font-size: 0.8em;
}
</style>
{% endblock %}
