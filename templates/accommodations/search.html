{% extends 'base.html' %}

{% block title %}Search Accommodations - NomadPersia{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">Discover Iran's Hidden Gems</h1>
                <p class="lead mb-4">Find the perfect accommodation for your journey through Tehran, Shiraz, and Mashhad</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-light">
            <h5 class="mb-0">🔍 Search & Filter Accommodations</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6 col-lg-3">
                    <label for="{{ form.search_query.id_for_label }}" class="form-label">Search</label>
                    {{ form.search_query }}
                </div>
                <div class="col-md-6 col-lg-2">
                    <label for="{{ form.city.id_for_label }}" class="form-label">City</label>
                    {{ form.city }}
                </div>
                <div class="col-md-6 col-lg-2">
                    <label for="{{ form.accommodation_type.id_for_label }}" class="form-label">Type</label>
                    {{ form.accommodation_type }}
                </div>
                <div class="col-md-6 col-lg-2">
                    <label for="{{ form.star_rating.id_for_label }}" class="form-label">Rating</label>
                    {{ form.star_rating }}
                </div>
                <div class="col-md-6 col-lg-3">
                    <label class="form-label">Price Range (IRR)</label>
                    <div class="row g-1">
                        <div class="col-6">{{ form.min_price }}</div>
                        <div class="col-6">{{ form.max_price }}</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-2">
                    <label for="{{ form.sort_by.id_for_label }}" class="form-label">Sort By</label>
                    {{ form.sort_by }}
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">🔍 Search</button>
                    <a href="{% url 'accommodations:search' %}" class="btn btn-outline-secondary">Clear</a>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Results Section -->
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h4>
                        {% if is_filtered %}
                            Search Results
                        {% else %}
                            All Accommodations
                        {% endif %}
                        <small class="text-muted">({{ total_count }} found)</small>
                    </h4>
                </div>
                {% if user.is_authenticated and user.profile.is_host %}
                <div>
                    <a href="{% url 'accommodations:create' %}" class="btn btn-success">
                        ➕ Add New Property
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    {% if accommodations %}
        <div class="row">
            {% for accommodation in accommodations %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card accommodation-card h-100 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title">{{ accommodation.name }}</h5>
                            {% if accommodation.star_rating %}
                                <span class="badge bg-warning text-dark">
                                    {% for i in "12345"|slice:":"|slice:accommodation.star_rating %}⭐{% endfor %}
                                </span>
                            {% endif %}
                        </div>
                        
                        <div class="mb-2">
                            <span class="badge bg-primary">{{ accommodation.get_accommodation_type_display }}</span>
                            <span class="badge bg-info">{{ accommodation.get_city_display }}{% if accommodation.town %}, {{ accommodation.town }}{% endif %}</span>
                        </div>
                        
                        <p class="card-text text-muted small">
                            {{ accommodation.description|truncatewords:20 }}
                        </p>
                        
                        {% if accommodation.get_amenities_list %}
                        <div class="mb-2">
                            <small class="text-muted">
                                <strong>Amenities:</strong>
                                {% for amenity in accommodation.get_amenities_list|slice:":3" %}
                                    {{ amenity }}{% if not forloop.last %}, {% endif %}
                                {% endfor %}
                                {% if accommodation.get_amenities_list|length > 3 %}...{% endif %}
                            </small>
                        </div>
                        {% endif %}

                        <!-- Photo Gallery Status -->
                        {% if "Professional photo gallery" in accommodation.image_placeholder %}
                        <div class="mb-2">
                            <small class="text-success">
                                <i class="fas fa-camera"></i> <strong>Photo Gallery Available</strong>
                                {% if "main photo" in accommodation.image_placeholder %}📸{% endif %}
                                {% if "interior photo" in accommodation.image_placeholder %}🏠{% endif %}
                                {% if "bedroom photo" in accommodation.image_placeholder %}🛏️{% endif %}
                                {% if "kitchen photo" in accommodation.image_placeholder %}🍳{% endif %}
                                {% if "bathroom photo" in accommodation.image_placeholder %}🚿{% endif %}
                                {% if "additional photos" in accommodation.image_placeholder %}📷{% endif %}
                            </small>
                        </div>
                        {% endif %}
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-primary mb-0">{{ accommodation.price_per_night|floatformat:0 }} IRR</h6>
                                <small class="text-muted">per night</small>
                            </div>
                            <a href="{% url 'accommodations:detail' accommodation.pk %}" class="btn btn-outline-primary btn-sm">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Accommodation pagination">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">Previous</a>
                    </li>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">Next</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <div class="mb-4" style="font-size: 4rem;">🏨</div>
            <h4>No accommodations found</h4>
            <p class="text-muted">Try adjusting your search criteria or browse all available properties.</p>
            {% if user.is_authenticated and user.profile.is_host %}
                <a href="{% url 'accommodations:create' %}" class="btn btn-primary">
                    List Your First Property
                </a>
            {% endif %}
        </div>
    {% endif %}
</div>

<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.accommodation-card {
    transition: transform 0.2s ease-in-out;
}

.accommodation-card:hover {
    transform: translateY(-5px);
}

.card {
    border: none;
    border-radius: 10px;
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}
