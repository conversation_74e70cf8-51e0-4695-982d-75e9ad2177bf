{% extends 'base.html' %}

{% block title %}Login - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-5 col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h3 class="mb-0">🔑 Welcome Back</h3>
                    <p class="mb-0 small">Sign in to NomadPersia</p>
                </div>
                <div class="card-body p-4">
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <strong>Lo<PERSON> failed:</strong>
                            <ul class="mb-0 mt-2">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                            <input type="text" class="form-control form-control-lg" 
                                   name="username" id="{{ form.username.id_for_label }}" 
                                   value="{{ form.username.value|default:'' }}" 
                                   placeholder="Enter your username" required autofocus>
                        </div>

                        <div class="mb-4">
                            <label for="{{ form.password.id_for_label }}" class="form-label">Password</label>
                            <input type="password" class="form-control form-control-lg" 
                                   name="password" id="{{ form.password.id_for_label }}" 
                                   placeholder="Enter your password" required>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="remember_me" id="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    Remember me
                                </label>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                🚀 Sign In
                            </button>
                        </div>
                    </form>

                    <!-- Additional Links -->
                    <div class="text-center mt-4">
                        <p class="text-muted mb-2">
                            <a href="#" class="text-decoration-none small">Forgot your password?</a>
                        </p>
                        <p class="text-muted">
                            Don't have an account? 
                            <a href="{% url 'accounts:register' %}" class="text-decoration-none">Sign Up</a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Demo Accounts -->
            <div class="card mt-4 bg-light">
                <div class="card-body">
                    <h6 class="text-center mb-3">🧪 Demo Accounts</h6>
                    <div class="row text-center small">
                        <div class="col-6">
                            <strong>Guest:</strong><br>
                            guest_sara / guest123
                        </div>
                        <div class="col-6">
                            <strong>Host:</strong><br>
                            host_tehran / host123
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            <strong>Admin:</strong> admin / admin123
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.form-control-lg {
    padding: 0.75rem 1rem;
    font-size: 1.1rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}
</style>
{% endblock %}
