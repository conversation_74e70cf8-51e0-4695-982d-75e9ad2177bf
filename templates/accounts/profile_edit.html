{% extends 'base.html' %}

{% block title %}Edit Profile - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h3 class="mb-0">✏️ Edit Profile</h3>
                    <small class="text-muted">Update your account information</small>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <strong>Please correct the errors below:</strong>
                            <ul class="mb-0 mt-2">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Personal Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="border-bottom pb-2">Personal Information</h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                <input type="text" class="form-control" name="first_name" 
                                       id="{{ form.first_name.id_for_label }}" 
                                       value="{{ form.first_name.value|default:user.first_name }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                <input type="text" class="form-control" name="last_name" 
                                       id="{{ form.last_name.id_for_label }}" 
                                       value="{{ form.last_name.value|default:user.last_name }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">Email Address</label>
                                <input type="email" class="form-control" name="email" 
                                       id="{{ form.email.id_for_label }}" 
                                       value="{{ form.email.value|default:user.email }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" name="phone_number" 
                                       id="{{ form.phone_number.id_for_label }}" 
                                       value="{{ form.phone_number.value|default:user.profile.phone_number }}"
                                       placeholder="+989123456789">
                                <small class="form-text text-muted">Include country code (e.g., +98 for Iran)</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">Date of Birth</label>
                                <input type="date" class="form-control" name="date_of_birth" 
                                       id="{{ form.date_of_birth.id_for_label }}" 
                                       value="{{ form.date_of_birth.value|default:user.profile.date_of_birth|date:'Y-m-d' }}">
                            </div>
                        </div>

                        <!-- Bio Section -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">About Me</h5>
                            <div class="mb-3">
                                <label for="{{ form.bio.id_for_label }}" class="form-label">Bio</label>
                                <textarea class="form-control" name="bio" id="{{ form.bio.id_for_label }}" 
                                          rows="4" placeholder="Tell others about yourself...">{{ form.bio.value|default:user.profile.bio }}</textarea>
                                <small class="form-text text-muted">
                                    {% if user.profile.is_guest %}
                                        Share your travel interests and what you're looking for in accommodations.
                                    {% elif user.profile.is_host %}
                                        Describe your hosting experience and what makes your properties special.
                                    {% endif %}
                                </small>
                            </div>
                        </div>

                        <!-- Account Information (Read-only) -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">Account Information</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Username</label>
                                    <input type="text" class="form-control" value="{{ user.username }}" readonly>
                                    <small class="form-text text-muted">Username cannot be changed</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Role</label>
                                    <input type="text" class="form-control" 
                                           value="{% if user.profile.is_guest %}Guest{% elif user.profile.is_host %}Host{% else %}Admin{% endif %}" readonly>
                                    <small class="form-text text-muted">Contact support to change your role</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Member Since</label>
                                    <input type="text" class="form-control" value="{{ user.date_joined|date:'F d, Y' }}" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Last Updated</label>
                                    <input type="text" class="form-control" value="{{ user.profile.updated_at|date:'F d, Y g:i A' }}" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'accounts:profile' %}" class="btn btn-secondary">
                                ← Cancel
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                💾 Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Additional Actions -->
            <div class="card mt-4 shadow">
                <div class="card-header">
                    <h5 class="mb-0">🔧 Additional Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Security</h6>
                            <p class="text-muted small">Manage your account security settings</p>
                            <a href="{% url 'accounts:password_change' %}" class="btn btn-outline-warning btn-sm">
                                🔒 Change Password
                            </a>
                        </div>
                        <div class="col-md-6">
                            <h6>Account Management</h6>
                            <p class="text-muted small">Advanced account options</p>
                            <button class="btn btn-outline-danger btn-sm" onclick="alert('Contact support for account deletion')">
                                🗑️ Delete Account
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Tips -->
            <div class="card mt-4 bg-light">
                <div class="card-body">
                    <h6>💡 Profile Tips</h6>
                    <div class="row">
                        {% if user.profile.is_guest %}
                        <div class="col-md-4">
                            <h6 class="small">📸 Add Photo</h6>
                            <p class="small text-muted">Profile photos help hosts recognize you</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="small">📝 Complete Bio</h6>
                            <p class="small text-muted">Share your travel style and interests</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="small">📞 Verify Phone</h6>
                            <p class="small text-muted">Verified contacts build trust</p>
                        </div>
                        {% elif user.profile.is_host %}
                        <div class="col-md-4">
                            <h6 class="small">🏨 Host Experience</h6>
                            <p class="small text-muted">Describe your hosting background</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="small">🌟 Hospitality Style</h6>
                            <p class="small text-muted">What makes your properties special?</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="small">📍 Local Knowledge</h6>
                            <p class="small text-muted">Share your local expertise</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.form-control[readonly] {
    background-color: #f8f9fa;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}
</style>
{% endblock %}
