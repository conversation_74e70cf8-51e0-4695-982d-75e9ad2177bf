<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Homyla - Travel Accommodation Booking{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #f8f9fa;
            --accent-color: #e74c3c;
            --text-color: #333;
            --border-color: #dee2e6;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-color);
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #1e3d6f;
            border-color: #1e3d6f;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            margin-top: 3rem;
        }
        
        .alert {
            border-radius: 0.5rem;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), #3498db);
            color: white;
            padding: 4rem 0;
        }
        
        .accommodation-card {
            transition: transform 0.2s;
        }
        
        .accommodation-card:hover {
            transform: translateY(-2px);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="{% url 'accommodations:search' %}">
                🏛️ Homyla Homyla
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accommodations:search' %}">Search</a>
                    </li>
                    {% if user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'bookings:my_bookings' %}">My Bookings</a>
                        </li>
                        {% if user.profile.is_host %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'dashboard:host_dashboard' %}">Host Dashboard</a>
                        </li>
                        {% endif %}
                        {% if user.profile.is_admin_user %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'dashboard:admin' %}">Admin Dashboard</a>
                        </li>
                        {% endif %}
                    {% endif %}
                    {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'bookings:my_bookings' %}">My Bookings</a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                👤 {{ user.first_name|default:user.username }}
                                {% if user.profile %}
                                    <small class="text-muted">({{ user.profile.get_role_display }})</small>
                                {% endif %}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">Profile</a></li>
                                {% if user.profile.is_host %}
                                <li><a class="dropdown-item" href="{% url 'dashboard:host_dashboard' %}">Host Dashboard</a></li>
                                {% endif %}
                                {% if user.profile.is_admin_user %}
                                <li><a class="dropdown-item" href="{% url 'dashboard:admin' %}">Admin Dashboard</a></li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">Logout</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:login' %}">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="{% url 'accounts:register' %}">Sign Up</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container py-4">
            <div class="row">
                <div class="col-md-6">
                    <h5>NomadPersia</h5>
                    <p class="mb-0">Your gateway to authentic Iranian hospitality</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <a href="#" class="text-white me-3">About</a>
                        <a href="#" class="text-white me-3">Contact</a>
                        <a href="#" class="text-white">Help</a>
                    </p>
                </div>
            </div>
            <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
            <div class="text-center">
                <small>&copy; 2024 NomadPersia. All rights reserved.</small>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
