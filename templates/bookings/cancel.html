{% extends 'base.html' %}

{% block title %}Cancel Booking - {{ booking.accommodation.name }} - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Booking Cancellation Card -->
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h3 class="mb-0">⚠️ Cancel Booking</h3>
                    <small class="text-muted">Are you sure you want to cancel this booking?</small>
                </div>
                <div class="card-body">
                    <!-- Booking Details Summary -->
                    <div class="booking-summary mb-4">
                        <h5 class="border-bottom pb-2">📋 Booking Details</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <strong>Accommodation:</strong><br>
                                    <span class="text-primary">{{ booking.accommodation.name }}</span>
                                </div>
                                <div class="mb-3">
                                    <strong>Location:</strong><br>
                                    {{ booking.accommodation.get_city_display }}{% if booking.accommodation.town %}, {{ booking.accommodation.town }}{% endif %}
                                </div>
                                <div class="mb-3">
                                    <strong>Booking Status:</strong><br>
                                    <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% else %}secondary{% endif %}">
                                        {{ booking.get_status_display }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <strong>Check-in:</strong><br>
                                    {{ booking.check_in_date|date:"F d, Y" }}
                                </div>
                                <div class="mb-3">
                                    <strong>Check-out:</strong><br>
                                    {{ booking.check_out_date|date:"F d, Y" }}
                                </div>
                                <div class="mb-3">
                                    <strong>Guests:</strong><br>
                                    {{ booking.get_guest_breakdown }}
                                </div>
                                <div class="mb-3">
                                    <strong>Total Price:</strong><br>
                                    <span class="h5 text-success">{{ booking.total_price|floatformat:0 }} IRR</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cancellation Policy Information -->
                    <div class="cancellation-policy mb-4">
                        <h5 class="border-bottom pb-2">📜 Cancellation Policy</h5>
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-md-8">
                                    <strong>Cancellation Terms:</strong>
                                    <ul class="mb-0 mt-2">
                                        <li>Free cancellation up to 24 hours before check-in</li>
                                        <li>Partial refund for cancellations within 24 hours</li>
                                        <li>No refund for no-shows</li>
                                        <li>Processing may take 3-5 business days</li>
                                    </ul>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="refund-estimate">
                                        <strong>Estimated Refund:</strong><br>
                                        <span class="h4 text-success">{{ booking.total_price|floatformat:0 }} IRR</span><br>
                                        <small class="text-muted">Full refund available</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cancellation Form -->
                    <form method="post" id="cancellationForm">
                        {% csrf_token %}
                        
                        <!-- Cancellation Reason -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">💬 Cancellation Reason (Optional)</h5>
                            <div class="mb-3">
                                <label for="reason" class="form-label">Why are you cancelling this booking?</label>
                                <textarea class="form-control" name="reason" id="reason" rows="4" 
                                          placeholder="Please let us know why you're cancelling (optional)..."></textarea>
                                <small class="form-text text-muted">
                                    This helps us improve our service and may be shared with the host.
                                </small>
                            </div>
                        </div>

                        <!-- Confirmation Checkbox -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirmCancellation" required>
                                <label class="form-check-label" for="confirmCancellation">
                                    <strong>I understand that this action cannot be undone and I want to cancel this booking.</strong>
                                </label>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'bookings:detail' booking.pk %}" class="btn btn-secondary btn-lg">
                                ← Back to Booking
                            </a>
                            <button type="submit" class="btn btn-danger btn-lg" id="cancelButton" disabled>
                                🗑️ Cancel Booking
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Important Notes -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">📌 Important Notes</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="small text-muted mb-0">
                                <li>Cancellation confirmation will be sent to your email</li>
                                <li>Refunds are processed to the original payment method</li>
                                <li>You can rebook the same accommodation anytime</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="small text-muted mb-0">
                                <li>Host will be notified of the cancellation</li>
                                <li>Your booking history will show this as cancelled</li>
                                <li>Contact support if you need assistance</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Enable cancel button only when confirmation is checked
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmCancellation');
    const cancelButton = document.getElementById('cancelButton');
    
    confirmCheckbox.addEventListener('change', function() {
        cancelButton.disabled = !this.checked;
    });
    
    // Add confirmation dialog
    document.getElementById('cancellationForm').addEventListener('submit', function(e) {
        if (!confirm('Are you absolutely sure you want to cancel this booking? This action cannot be undone.')) {
            e.preventDefault();
        }
    });
});
</script>

<style>
.card {
    border: none;
    border-radius: 12px;
}

.card-header {
    border-radius: 12px 12px 0 0;
}

.booking-summary {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.refund-estimate {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 2px solid #28a745;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

.form-check-input:checked {
    background-color: #dc3545;
    border-color: #dc3545;
}

.alert-info {
    border-left: 4px solid #17a2b8;
}
</style>
{% endblock %}
