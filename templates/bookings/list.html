{% extends 'base.html' %}

{% block title %}My Bookings - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>📅 My Bookings</h2>
                    <p class="text-muted">Manage your accommodation reservations</p>
                </div>
                <div>
                    <a href="{% url 'accommodations:search' %}" class="btn btn-primary">
                        🔍 Find More Accommodations
                    </a>
                </div>
            </div>

            <!-- Bookings List -->
            {% if bookings %}
                <div class="row">
                    {% for booking in bookings %}
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">{{ booking.accommodation.name }}</h5>
                                <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'completed' %}primary{% else %}danger{% endif %}">
                                    {{ booking.get_status_display }}
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">Check-in</small>
                                        <div class="fw-bold">{{ booking.check_in_date|date:"M d, Y" }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Check-out</small>
                                        <div class="fw-bold">{{ booking.check_out_date|date:"M d, Y" }}</div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">Guests</small>
                                        <div>{{ booking.number_of_guests }} guest{{ booking.number_of_guests|pluralize }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Duration</small>
                                        <div>{{ booking.duration_nights }} night{{ booking.duration_nights|pluralize }}</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">Total Price</small>
                                    <div class="h5 text-primary mb-0">{{ booking.total_price|floatformat:0 }} IRR</div>
                                </div>

                                {% if booking.special_requests %}
                                <div class="mb-3">
                                    <small class="text-muted">Special Requests</small>
                                    <div class="small">{{ booking.special_requests }}</div>
                                </div>
                                {% endif %}

                                <div class="mb-3">
                                    <small class="text-muted">Booking Date</small>
                                    <div class="small">{{ booking.created_at|date:"M d, Y g:i A" }}</div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="d-flex gap-2">
                                    <a href="{% url 'bookings:detail' booking.id %}" class="btn btn-outline-primary btn-sm">
                                        👁️ View Details
                                    </a>
                                    {% if booking.can_be_cancelled %}
                                    <a href="{% url 'bookings:cancel' booking.id %}" class="btn btn-outline-danger btn-sm"
                                       onclick="return confirm('Are you sure you want to cancel this booking?')">
                                        ❌ Cancel
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <!-- No Bookings -->
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="display-1">📅</i>
                    </div>
                    <h3>No Bookings Found</h3>
                    <p class="text-muted mb-4">
                        You haven't made any bookings yet. Start exploring amazing accommodations!
                    </p>
                    <div class="d-flex gap-2 justify-content-center">
                        <a href="{% url 'accommodations:search' %}" class="btn btn-primary">
                            🔍 Find Accommodations
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.badge {
    font-size: 0.8em;
}
</style>
{% endblock %}
