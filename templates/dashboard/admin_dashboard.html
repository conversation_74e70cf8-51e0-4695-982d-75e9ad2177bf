{% extends 'base.html' %}

{% block title %}Admin Dashboard - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">🛠️ Admin Dashboard</h2>
                            <p class="mb-0">Platform management and oversight</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="h4 mb-0">{{ stats.total_users|default:0 }} Users</div>
                            <small>{{ stats.total_accommodations|default:0 }} Properties</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Platform Stats -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="display-6 text-primary mb-2">👥</div>
                    <h3 class="mb-1">{{ stats.total_users|default:0 }}</h3>
                    <p class="text-muted mb-0">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="display-6 text-success mb-2">🏨</div>
                    <h3 class="mb-1">{{ stats.total_accommodations|default:0 }}</h3>
                    <p class="text-muted mb-0">Properties</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="display-6 text-info mb-2">📅</div>
                    <h3 class="mb-1">{{ stats.total_bookings|default:0 }}</h3>
                    <p class="text-muted mb-0">Bookings</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="display-6 text-warning mb-2">⏳</div>
                    <h3 class="mb-1">{{ stats.pending_accommodations|default:0 }}</h3>
                    <p class="text-muted mb-0">Pending Approval</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Quick Actions -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">🚀 Admin Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'dashboard:admin_users' %}" class="btn btn-primary">
                            👥 Manage Users
                        </a>
                        <a href="{% url 'dashboard:admin_accommodations' %}" class="btn btn-outline-primary">
                            🏨 Moderate Properties
                        </a>
                        <a href="{% url 'dashboard:admin_bookings' %}" class="btn btn-outline-success">
                            📅 View All Bookings
                        </a>
                        <a href="{% url 'dashboard:site_settings' %}" class="btn btn-outline-secondary">
                            ⚙️ Site Settings
                        </a>
                        <a href="/admin/" class="btn btn-outline-warning" target="_blank">
                            🔧 Django Admin
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">📋 Recent Activity</h5>
                </div>
                <div class="card-body">
                    {% if recent_activities %}
                        <div class="list-group list-group-flush">
                            {% for activity in recent_activities %}
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ activity.description }}</h6>
                                        <p class="mb-1 small text-muted">{{ activity.user|default:"System" }}</p>
                                    </div>
                                    <small class="text-muted">{{ activity.created_at|timesince }} ago</small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <div class="mb-3">📋</div>
                            <p class="text-muted">No recent activity</p>
                            <p class="small text-muted">System activity and user actions will appear here</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Platform Overview -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📊 User Distribution</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <h4 class="text-primary">{{ stats.guest_users|default:0 }}</h4>
                            <small class="text-muted">Guests</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-success">{{ stats.host_users|default:0 }}</h4>
                            <small class="text-muted">Hosts</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-warning">{{ stats.admin_users|default:0 }}</h4>
                            <small class="text-muted">Admins</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">🏙️ Properties by City</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <h4 class="text-primary">{{ stats.tehran_properties|default:0 }}</h4>
                            <small class="text-muted">Tehran</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-success">{{ stats.shiraz_properties|default:0 }}</h4>
                            <small class="text-muted">Shiraz</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-info">{{ stats.mashhad_properties|default:0 }}</h4>
                            <small class="text-muted">Mashhad</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Health -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">🔧 System Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <div class="mb-2 text-success">✅</div>
                            <h6>Database</h6>
                            <p class="small text-muted">Operational</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="mb-2 text-success">✅</div>
                            <h6>Email Service</h6>
                            <p class="small text-muted">Operational</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="mb-2 text-success">✅</div>
                            <h6>File Storage</h6>
                            <p class="small text-muted">Operational</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="mb-2 text-success">✅</div>
                            <h6>Security</h6>
                            <p class="small text-muted">All systems secure</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transition: box-shadow 0.15s ease-in-out;
}

.display-6 {
    font-size: 2rem;
}

.list-group-item {
    border: none !important;
}
</style>
{% endblock %}

