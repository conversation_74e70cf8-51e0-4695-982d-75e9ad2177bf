{% extends 'base.html' %}
{% load static %}

{% block title %}Reviews - {{ accommodation.name }}{% endblock %}

{% block extra_css %}
<style>
.star-rating {
    color: #ffc107;
    font-size: 1.2em;
}
.star-rating-small {
    color: #ffc107;
    font-size: 0.9em;
}
.review-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 20px;
    transition: box-shadow 0.3s ease;
}
.review-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.rating-breakdown {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}
.rating-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}
.rating-fill {
    height: 100%;
    background: #ffc107;
    transition: width 0.3s ease;
}
.host-response {
    background: #f0f8ff;
    border-left: 4px solid #007bff;
    padding: 15px;
    margin-top: 15px;
    border-radius: 0 8px 8px 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'accommodations:search' %}">Accommodations</a></li>
            <li class="breadcrumb-item"><a href="{% url 'accommodations:detail' accommodation.id %}">{{ accommodation.name }}</a></li>
            <li class="breadcrumb-item active">Reviews</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>Guest Reviews</h2>
            <p class="text-muted">{{ accommodation.name }}</p>
        </div>
        <div class="col-md-4 text-end">
            {% if can_review %}
                <a href="{% url 'reviews:create_review' accommodation.id %}" class="btn btn-primary">
                    <i class="fas fa-star"></i> Write a Review
                </a>
            {% endif %}
        </div>
    </div>

    <!-- Rating Summary -->
    {% if average_rating %}
    <div class="rating-breakdown">
        <div class="row">
            <div class="col-md-4 text-center">
                <div class="display-4 fw-bold text-primary">{{ average_rating|floatformat:1 }}</div>
                <div class="star-rating mb-2">
                    {% for i in "12345" %}
                        {% if forloop.counter <= average_rating|floatformat:0 %}
                            ★
                        {% else %}
                            ☆
                        {% endif %}
                    {% endfor %}
                </div>
                <div class="text-muted">{{ total_reviews }} reviews</div>
            </div>
            <div class="col-md-8">
                <h5>Rating Distribution</h5>
                {% for rating in "54321" %}
                <div class="row align-items-center mb-2">
                    <div class="col-2">
                        <small>{{ rating }} stars</small>
                    </div>
                    <div class="col-8">
                        <div class="rating-bar">
                            <div class="rating-fill" style="width: {% widthratio rating 5 100 %}%"></div>
                        </div>
                    </div>
                    <div class="col-2">
                        <small class="text-muted">0</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Reviews List -->
    <div class="reviews-list">
        {% if reviews %}
            {% for review in reviews %}
            <div class="review-card p-4">
                <div class="row">
                    <div class="col-md-2 text-center">
                        <div class="mb-2">
                            <i class="fas fa-user-circle fa-3x text-muted"></i>
                        </div>
                        <div class="fw-bold">{{ review.guest.get_full_name|default:review.guest.username }}</div>
                        <small class="text-muted">{{ review.created_at|date:"F Y" }}</small>
                    </div>
                    <div class="col-md-10">
                        <!-- Overall Rating -->
                        <div class="mb-3">
                            <div class="star-rating">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= review.overall_rating %}
                                        ★
                                    {% else %}
                                        ☆
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <span class="ms-2 text-muted">{{ review.created_at|date:"j F Y" }}</span>
                        </div>

                        <!-- Category Ratings -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small>Cleanliness:</small>
                                    <div class="star-rating-small">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.cleanliness_rating %}★{% else %}☆{% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small>Location:</small>
                                    <div class="star-rating-small">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.location_rating %}★{% else %}☆{% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small>Value:</small>
                                    <div class="star-rating-small">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.value_rating %}★{% else %}☆{% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small>Communication:</small>
                                    <div class="star-rating-small">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.communication_rating %}★{% else %}☆{% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small>Check-in:</small>
                                    <div class="star-rating-small">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.checkin_rating %}★{% else %}☆{% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small>Accuracy:</small>
                                    <div class="star-rating-small">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.accuracy_rating %}★{% else %}☆{% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Review Text -->
                        {% if review.review_text %}
                        <div class="mb-3">
                            <p>{{ review.review_text|linebreaks }}</p>
                        </div>
                        {% endif %}

                        <!-- Host Response -->
                        {% if review.host_response %}
                        <div class="host-response">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-home text-primary me-2"></i>
                                <strong>Host Response</strong>
                                <small class="text-muted ms-2">{{ review.host_response_date|date:"F j, Y" }}</small>
                            </div>
                            <p class="mb-0">{{ review.host_response|linebreaks }}</p>
                        </div>
                        {% endif %}

                        <!-- Review Actions -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                {% if user.is_authenticated %}
                                    {% if user == review.guest %}
                                        <a href="{% url 'reviews:edit_review' review.id %}" class="btn btn-sm btn-outline-primary">Edit</a>
                                        <a href="{% url 'reviews:delete_review' review.id %}" class="btn btn-sm btn-outline-danger">Delete</a>
                                    {% elif user == accommodation.host and not review.host_response %}
                                        <a href="{% url 'reviews:respond_to_review' review.id %}" class="btn btn-sm btn-outline-primary">Respond</a>
                                    {% endif %}
                                {% endif %}
                            </div>
                            <div>
                                <small class="text-muted">Was this review helpful?</small>
                                <button class="btn btn-sm btn-outline-success ms-1" onclick="markHelpful({{ review.id }}, true)">
                                    <i class="fas fa-thumbs-up"></i> Yes
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="markHelpful({{ review.id }}, false)">
                                    <i class="fas fa-thumbs-down"></i> No
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            <!-- Pagination -->
            {% if reviews.has_other_pages %}
            <nav aria-label="Reviews pagination">
                <ul class="pagination justify-content-center">
                    {% if reviews.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ reviews.previous_page_number }}">قبلی</a>
                        </li>
                    {% endif %}
                    
                    {% for num in reviews.paginator.page_range %}
                        {% if reviews.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if reviews.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ reviews.next_page_number }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-star fa-3x text-muted mb-3"></i>
                <h4>No Reviews Yet</h4>
                <p class="text-muted">Be the first to share your experience about this accommodation.</p>
                {% if can_review %}
                    <a href="{% url 'reviews:create_review' accommodation.id %}" class="btn btn-primary">
                        Write a Review
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function markHelpful(reviewId, isHelpful) {
    fetch(`/reviews/helpful/${reviewId}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: `is_helpful=${isHelpful}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI to show vote was recorded
            console.log('Vote recorded successfully');
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
{% endblock %}
